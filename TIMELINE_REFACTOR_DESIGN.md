# Timeline Refactorizado - Diseño y Estructura

## 🎯 Objetivos de la Refactorización

1. **Separar responsabilidades**: TimelineItem vs TransitionElement
2. **Mejorar legibilidad**: Estructura más declarativa
3. **Facilitar mantenimiento**: Componentes más pequeños y enfocados
4. **Mantener funcionalidad**: Sin cambios en UX actual

## 📊 Estructura de Datos

```typescript
// Estructura actual (mantener)
interface TimelineItem {
  id: string;
  imageId: string;
  duration: number;
  position: number;
  transition?: TransitionConfig;
}

// Nueva estructura para el renderizado
interface TimelineRenderItem {
  type: 'image' | 'transition';
  id: string;
  data: TimelineItem | TransitionData;
  index: number;
}

interface TransitionData {
  id: string;
  fromItemId: string;
  toItemId: string;
  config: TransitionConfig;
}
```

## 🏗️ Arquitectura de Componentes

```
Timeline (Container)
├── TimelineControls (nuevo - opcional para futuro)
├── TimelineTrack (nuevo - contenedor de items)
│   ├── TimelineItem (refactorizado)
│   │   ├── ImageThumbnail
│   │   ├── DurationControl
│   │   ├── RemoveButton
│   │   └── DragHandle
│   └── TransitionElement (nuevo)
│       ├── TransitionIcon
│       ├── TransitionLabel
│       └── EditButton
└── TransitionModal (existente - sin cambios)
```

## 🎨 Disposición Visual

```
┌─────────────────────────────────────────────────────────────────┐
│                        Timeline Container                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌────────┐    ┌──────┐    ┌────────┐    ┌──────┐    ┌────────┐ │
│  │ Image1 │───▶│ Fade │───▶│ Image2 │───▶│Slide │───▶│ Image3 │ │
│  │ 120x80 │    │  🔄  │    │ 120x80 │    │  🔄  │    │ 120x80 │ │
│  │  1.5s  │    │      │    │  2.0s  │    │      │    │  1.0s  │ │
│  │   ×    │    │  ⚙️   │    │   ×    │    │  ⚙️   │    │   ×    │ │
│  └────────┘    └──────┘    └────────┘    └──────┘    └────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 💻 Implementación - TimelineItem Refactorizado

```typescript
interface TimelineItemProps {
  item: TimelineItem;
  image: ImageFile;
  index: number;
  dragState: DragState;
  handlers: {
    drag: DragHandlers;
    actions: ActionHandlers;
    ui: UIHandlers;
  };
  utilities: {
    formatDuration: (duration: number) => string;
  };
}

const TimelineItem: React.FC<TimelineItemProps> = ({
  item,
  image,
  index,
  dragState,
  handlers,
  utilities
}) => {
  return (
    <div className="timeline-item" data-item-id={item.id}>
      <ImageThumbnail 
        src={image.preview}
        alt={image.name}
        isDragged={dragState.isDragged}
        onDragStart={handlers.drag.onDragStart}
        onDragEnd={handlers.drag.onDragEnd}
      />
      
      <DurationControl
        duration={item.duration}
        formatDuration={utilities.formatDuration}
        onChange={handlers.actions.onDurationChange}
      />
      
      <RemoveButton
        onRemove={() => handlers.actions.onRemove(item.id)}
        isVisible={dragState.isHovered}
      />
    </div>
  );
};
```

## 💻 Implementación - TransitionElement (Nuevo)

```typescript
interface TransitionElementProps {
  fromItem: TimelineItem;
  toItem: TimelineItem;
  index: number;
  handlers: {
    onEdit: (itemId: string) => void;
  };
}

const TransitionElement: React.FC<TransitionElementProps> = ({
  fromItem,
  toItem,
  index,
  handlers
}) => {
  const transition = fromItem.transition;
  
  return (
    <div className="transition-element">
      <TransitionIcon type={transition?.type || 'none'} />
      
      <TransitionLabel 
        text={transition?.type || 'No transition'}
        isActive={!!transition}
      />
      
      <EditButton
        onClick={() => handlers.onEdit(fromItem.id)}
        frameNumber={index + 1}
      />
    </div>
  );
};
```

## 💻 Timeline Container Refactorizado

```typescript
const Timeline: React.FC = () => {
  const { project, updateTimelineItem, removeFromTimeline, reorderTimeline } = useSlideshowContext();
  const [dragState, setDragState] = useState<DragState>(initialDragState);
  const [transitionModal, setTransitionModal] = useState<TransitionModalState>(initialModalState);

  // Convertir timeline a estructura de renderizado
  const renderItems = useMemo(() => {
    const items: TimelineRenderItem[] = [];
    
    project.timeline.forEach((item, index) => {
      // Agregar item de imagen
      items.push({
        type: 'image',
        id: item.id,
        data: item,
        index
      });
      
      // Agregar transición si no es el último item
      if (index < project.timeline.length - 1) {
        items.push({
          type: 'transition',
          id: `transition-${item.id}`,
          data: {
            id: `transition-${item.id}`,
            fromItemId: item.id,
            toItemId: project.timeline[index + 1].id,
            config: item.transition
          },
          index
        });
      }
    });
    
    return items;
  }, [project.timeline]);

  // Handlers agrupados
  const handlers = {
    drag: {
      onDragStart: handleDragStart,
      onDragEnd: handleDragEnd,
      onDragOver: handleDragOver,
      onDrop: handleDrop
    },
    actions: {
      onRemove: removeFromTimeline,
      onDurationChange: (itemId: string, duration: number) => 
        updateTimelineItem(itemId, { duration }),
      onTransitionEdit: (itemId: string) => 
        setTransitionModal({
          isOpen: true,
          itemId,
          frameNumber: project.timeline.findIndex(item => item.id === itemId) + 1
        })
    }
  };

  return (
    <div className="timeline-container">
      <div className="timeline-track">
        {renderItems.map((renderItem) => {
          if (renderItem.type === 'image') {
            const item = renderItem.data as TimelineItem;
            const image = project.images.find(img => img.id === item.imageId);
            
            return image ? (
              <TimelineItem
                key={renderItem.id}
                item={item}
                image={image}
                index={renderItem.index}
                dragState={getDragStateForItem(item.id, dragState)}
                handlers={handlers}
                utilities={{ formatDuration }}
              />
            ) : null;
          } else {
            const transitionData = renderItem.data as TransitionData;
            const fromItem = project.timeline.find(item => item.id === transitionData.fromItemId);
            const toItem = project.timeline.find(item => item.id === transitionData.toItemId);
            
            return fromItem && toItem ? (
              <TransitionElement
                key={renderItem.id}
                fromItem={fromItem}
                toItem={toItem}
                index={renderItem.index}
                handlers={{
                  onEdit: handlers.actions.onTransitionEdit
                }}
              />
            ) : null;
          }
        })}
      </div>
      
      <TransitionModal
        isOpen={transitionModal.isOpen}
        onClose={() => setTransitionModal(initialModalState)}
        onSave={(transition) => {
          updateTimelineItem(transitionModal.itemId, { transition });
          setTransitionModal(initialModalState);
        }}
        currentTransition={project.timeline.find(item => item.id === transitionModal.itemId)?.transition}
        frameNumber={transitionModal.frameNumber}
      />
    </div>
  );
};
```

## 🎨 Estilos CSS

```css
.timeline-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.timeline-track {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow-x: auto;
  padding: 8px 0;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex-shrink: 0;
}

.transition-element {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 80px;
  background: rgba(55, 65, 81, 0.5);
  border: 1px solid #374151;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.transition-element:hover {
  background: rgba(236, 72, 153, 0.1);
  border-color: #ec4899;
}
```

## ✅ Ventajas de esta Refactorización

1. **Separación clara**: TimelineItem vs TransitionElement
2. **Mejor organización**: Handlers agrupados por responsabilidad
3. **Más declarativo**: Estructura de renderizado explícita
4. **Fácil testing**: Componentes más pequeños y enfocados
5. **Extensible**: Fácil agregar nuevas características
6. **Mantiene UX**: Sin cambios visuales para el usuario

## 🚀 Plan de Implementación

1. **Crear componentes base**: TimelineItem, TransitionElement
2. **Refactorizar Timeline container**: Nueva lógica de renderizado
3. **Migrar estilos**: CSS modular
4. **Testing**: Validar funcionalidad existente
5. **Cleanup**: Remover código obsoleto
