# Script de Migración - Timeline Refactorizado

## 🎯 Objetivo
Facilitar la migración del Timeline original al Timeline refactorizado de forma segura y reversible.

## 📋 Pasos de Migración

### Paso 1: Backup del Timeline Original
```bash
# Crear backup del Timeline actual (si no existe)
cp frontend/src/slideshow/components/Timeline.tsx frontend/src/slideshow/components/Timeline.backup.tsx
```

### Paso 2: Actualizar Import en SlideshowApp
```typescript
// En frontend/src/slideshow/SlideshowApp.tsx
// CAMBIAR:
import Timeline from './components/Timeline';

// POR:
import Timeline from './components/timeline/TimelineRefactored';
```

### Paso 3: Verificar Dependencias
Asegurar que todos los imports estén correctos:
- `TimelineItem` → `./components/timeline/TimelineItem`
- `TransitionElement` → `./components/timeline/TransitionElement`
- `TransitionModal` → `./components/TransitionModal` (sin cambios)

### Paso 4: Pruebas de Funcionalidad
Ejecutar el checklist de pruebas del archivo `TIMELINE_REFACTOR_TEST_PLAN.md`

## 🔧 Comandos de Migración

### Migración Completa (Un Solo Comando)
```bash
# Ejecutar desde la raíz del proyecto
cd frontend/src/slideshow/components

# 1. Backup del original
cp Timeline.tsx Timeline.backup.tsx

# 2. Reemplazar Timeline con versión refactorizada
cp timeline/TimelineRefactored.tsx Timeline.tsx

# 3. Actualizar import en Timeline.tsx para que sea compatible
sed -i '' 's/TimelineRefactored/Timeline/g' Timeline.tsx
```

### Rollback (Si es Necesario)
```bash
# Revertir cambios
cd frontend/src/slideshow/components
cp Timeline.backup.tsx Timeline.tsx
```

## 📝 Checklist de Migración

### Pre-Migración
- [ ] **Backup creado**: Timeline original respaldado
- [ ] **Tests funcionando**: Timeline actual pasa todas las pruebas
- [ ] **Estado limpio**: No hay cambios sin commitear

### Durante Migración
- [ ] **Import actualizado**: SlideshowApp usa nuevo Timeline
- [ ] **Dependencias correctas**: Todos los imports funcionan
- [ ] **Sin errores de compilación**: Proyecto compila sin errores
- [ ] **Sin warnings**: No hay warnings nuevos en consola

### Post-Migración
- [ ] **Funcionalidad completa**: Todas las funciones del Timeline funcionan
- [ ] **Performance aceptable**: No degradación significativa
- [ ] **UX idéntica**: Experiencia de usuario sin cambios
- [ ] **Tests pasando**: Todas las pruebas pasan

## 🧪 Script de Validación

```bash
#!/bin/bash
# validate_timeline_migration.sh

echo "🔍 Validando migración del Timeline..."

# 1. Verificar que el proyecto compila
echo "📦 Verificando compilación..."
cd frontend && npm run build
if [ $? -ne 0 ]; then
    echo "❌ Error de compilación. Revisa los imports."
    exit 1
fi

# 2. Verificar que no hay errores de TypeScript
echo "🔧 Verificando TypeScript..."
cd frontend && npx tsc --noEmit
if [ $? -ne 0 ]; then
    echo "❌ Errores de TypeScript encontrados."
    exit 1
fi

# 3. Verificar que los archivos existen
echo "📁 Verificando archivos..."
FILES=(
    "src/slideshow/components/timeline/TimelineItem.tsx"
    "src/slideshow/components/timeline/TransitionElement.tsx"
    "src/slideshow/components/timeline/TimelineRefactored.tsx"
)

for file in "${FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Archivo faltante: $file"
        exit 1
    fi
done

echo "✅ Validación completada exitosamente!"
echo "🚀 Timeline refactorizado listo para usar."
```

## 🎨 Actualización de Estilos (Si es Necesario)

Si hay problemas de estilos, agregar estos CSS:

```css
/* En frontend/src/index.css o archivo de estilos global */

/* Timeline Container */
.timeline-container {
  background-color: #0a0a0b;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 16px;
}

/* Timeline Track */
.timeline-track {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 8px 0;
  min-height: 100px;
}

/* Timeline Item */
.timeline-item {
  position: relative;
  flex-shrink: 0;
}

/* Timeline Thumbnail */
.timeline-thumbnail {
  width: 120px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #374151;
  cursor: grab;
  transition: all 0.2s ease;
}

.timeline-thumbnail:active {
  cursor: grabbing;
}

/* Transition Element */
.transition-element {
  width: 60px;
  height: 80px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  position: relative;
}

/* Duration Control */
.duration-control input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: #374151;
  border-radius: 2px;
  height: 4px;
  outline: none;
}

.duration-control input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ec4899;
  cursor: pointer;
}

.duration-control input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ec4899;
  cursor: pointer;
  border: none;
}
```

## 🔄 Plan de Rollback Detallado

### Si la Migración Falla:

1. **Rollback Inmediato**:
   ```bash
   cd frontend/src/slideshow/components
   cp Timeline.backup.tsx Timeline.tsx
   ```

2. **Verificar Rollback**:
   ```bash
   cd frontend && npm run dev
   # Verificar que la aplicación funciona normalmente
   ```

3. **Documentar Issues**:
   - Crear issue en GitHub con detalles del problema
   - Incluir logs de error y pasos para reproducir
   - Marcar como blocker para futuras migraciones

4. **Fix Iterativo**:
   - Corregir issues en TimelineRefactored
   - Re-testear en ambiente de desarrollo
   - Repetir migración cuando esté listo

## 📊 Métricas de Éxito de la Migración

- **Tiempo de migración**: ≤15 minutos
- **Downtime**: 0 (desarrollo local)
- **Bugs introducidos**: 0 críticos, ≤1 menor
- **Performance**: Sin degradación >5%
- **UX**: Sin cambios no intencionados

## 🎉 Post-Migración

Una vez completada exitosamente:

1. **Cleanup**:
   ```bash
   # Opcional: Remover archivos de backup después de validación
   rm frontend/src/slideshow/components/Timeline.backup.tsx
   ```

2. **Documentación**:
   - Actualizar README con nueva estructura
   - Documentar cambios en CHANGELOG
   - Actualizar diagramas de arquitectura

3. **Commit**:
   ```bash
   git add .
   git commit -m "refactor: migrate Timeline to component-based architecture
   
   - Split Timeline into TimelineItem and TransitionElement
   - Improve code organization and maintainability
   - Maintain identical UX and functionality
   - Add comprehensive test coverage"
   ```
