import React from 'react';
import { ValidationMessages, ValidationSummary } from '../../../components/ValidationMessages';
import { ValidationResult as FullValidationResult } from '../../../hooks/useExportValidation';

export interface ValidationResult {
  isValid: boolean;
  canExport: boolean;
  messages: Array<{ type: 'error' | 'warning'; message: string }>;
  hasErrors: boolean;
  hasWarnings: boolean;
}

export interface ExportValidationDisplayProps {
  validation: ValidationResult | FullValidationResult;
}

const ExportValidationDisplay: React.FC<ExportValidationDisplayProps> = ({
  validation
}) => {
  // Only show validation messages if there are errors or warnings
  if (validation.messages.length === 0) {
    return null;
  }

  // Convert to full validation result if needed
  const fullValidation: FullValidationResult = 'field' in validation.messages[0] ?
    validation as FullValidationResult :
    {
      isValid: validation.isValid,
      canExport: validation.canExport,
      hasErrors: validation.hasErrors,
      hasWarnings: validation.hasWarnings,
      messages: validation.messages.map(m => ({
        type: m.type as 'error' | 'warning',
        field: 'general',
        message: m.message,
        code: m.type.toUpperCase()
      }))
    };

  return (
    <div className="mb-4">
      <ValidationSummary validation={fullValidation} />
      <ValidationMessages validation={fullValidation} />
    </div>
  );
};

export default ExportValidationDisplay;
