# Railway Configuration for AnimaGen
# Connects to GitHub repository: https://github.com/GsusFC/anima

[build]
builder = "NIXPACKS"
buildCommand = "cd backend && npm install && cd ../frontend && npm install && npm run build && mkdir -p ../backend/public && cp -r dist/* ../backend/public/"

[deploy]
startCommand = "cd backend && npm start"
healthcheckPath = "/api/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[env]
NODE_ENV = "production"
PORT = "$PORT"
OUTPUT_DIR = "output"
TEMP_DIR = "uploads"
MAX_FILE_SIZE = "104857600"
MAX_FILES = "100"
FFMPEG_THREADS = "2"
MAX_CONCURRENT_JOBS = "2"
CORS_ORIGINS = "$RAILWAY_PUBLIC_DOMAIN"
