[build]
builder = "NIXPACKS"
buildCommand = "npm run build:railway"

[deploy]
startCommand = "cd backend && npm start"
healthcheckPath = "/api/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[env]
NODE_ENV = "production"
PORT = "3001"
OUTPUT_DIR = "output"
TEMP_DIR = "uploads"
MAX_FILE_SIZE = "104857600"
MAX_FILES = "100"
FFMPEG_THREADS = "2"
MAX_CONCURRENT_JOBS = "2"
